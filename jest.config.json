{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "testTimeout": 600000, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "moduleNameMapper": {"^@common/(.*)$": "<rootDir>/src/common/$1", "^@orchestrator/(.*)$": "<rootDir>/src/orchestrator/$1", "^@intelligence/(.*)$": "<rootDir>/src/intelligence/$1", "^@business-base/(.*)$": "<rootDir>/src/business-base/$1", "^@auth/(.*)$": "<rootDir>/src/auth/$1", "^@message-hub/(.*)$": "<rootDir>/src/message-hub/$1", "^@data-insights/(.*)$": "<rootDir>/src/data-insights/$1", "^@integration-hub/(.*)$": "<rootDir>/src/integration-hub/$1", "@app.module": ["<rootDir>/src/app.module"], "^test/(.*)$": "<rootDir>/test/$1"}, "globalSetup": "<rootDir>/test/e2e/jest.global-setup.ts", "globalTeardown": "<rootDir>/test/e2e/jest.global-teardown.ts"}