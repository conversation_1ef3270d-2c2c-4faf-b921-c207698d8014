-- CreateTable
CREATE TABLE "message_hub"."phone_verification" (
    "id" UUID NOT NULL,
    "customer_id" UUID NOT NULL,
    "phone_number" TEXT NOT NULL,
    "verification_status" TEXT NOT NULL DEFAULT 'PENDING',
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "phone_verification_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "phone_verification_customerid_index" ON "message_hub"."phone_verification"("customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "phone_verification_phone_number_key" ON "message_hub"."phone_verification"("phone_number");
