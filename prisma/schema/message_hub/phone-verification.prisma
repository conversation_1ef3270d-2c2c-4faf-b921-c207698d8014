model phoneVerification {
  id                 String   @id @default(uuid()) @map(name: "id") @db.Uuid
  customerId         String   @map(name: "customer_id") @db.Uuid
  phoneNumber        String   @map(name: "phone_number")
  verificationStatus String   @default("PENDING") @map(name: "verification_status")
  status             String   @default("ACTIVE")
  createdAt          DateTime @default(now()) @map(name: "created_at")
  updatedAt          DateTime @updatedAt @map(name: "updated_at")

  @@unique([phoneNumber], name: "phone_verification_phone_number_key")
  @@index([customerId], name: "phone_verification_customerid_index")
  @@map(name: "phone_verification")
  @@schema("message_hub")
}
