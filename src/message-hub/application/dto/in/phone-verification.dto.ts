import { <PERSON>rrayMinSize, IsArray, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class PhoneVerificationDto {
  @IsString()
  @IsUUID()
  @IsNotEmpty()
  readonly customerId: string;

  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  @IsNotEmpty()
  readonly phoneNumbers: string[];

  constructor(customerId: string, phoneNumbers: string[]) {
    this.customerId = customerId;
    this.phoneNumbers = phoneNumbers;
  }
}
