import { Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { v4 as uuid } from 'uuid';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { PhoneVerificationPort } from '@message-hub/infrastructure/ports/db/phone-verification.port';
import { PhoneVerificationDto } from '@message-hub/application/dto/in/phone-verification.dto';
import { PhoneVerificationStatus, RecordStatus } from '@common/enums';
import { InfraCheckNumberPort } from '@message-hub/infrastructure/ports/http/check-number.port';
import { plainToClass } from 'class-transformer';
import { PhoneVerificationResponseDto } from '@message-hub/application/dto/out/phone-verification-response.dto';
import { PhoneVerificationEntity } from '@message-hub/domain/entities/phone-verification.entity';

@Injectable()
export class PhoneVerificationUseCase {
  constructor(
    @Inject('PhoneVerificationPort')
    private readonly customerVerificationAdapter: PhoneVerificationPort,
    @Inject('InfraCheckNumberPort')
    private readonly checkNumberAdapter: InfraCheckNumberPort,
  ) {}

  async createPhonesVerification(
    phoneVerificationDto: PhoneVerificationDto,
  ): Promise<{ id: string; phoneNumber: string }[]> {
    logger.info(`Creating phone verification for customer:  ${phoneVerificationDto.customerId}`);

    const existentPhonesVerification = await this.customerVerificationAdapter.getAll({
      phoneNumber: { in: phoneVerificationDto.phoneNumbers },
    });

    const existentPhoneNumbers = existentPhonesVerification.map(phone => phone.phoneNumber);

    if (existentPhoneNumbers.length > 0) {
      logger.info(
        `The following phone numbers already have a verification request and will not be created: ${existentPhoneNumbers.join(
          ', ',
        )}`,
      );
    }

    const requestWithoutExistentPhones = phoneVerificationDto.phoneNumbers.filter(
      phone => !existentPhoneNumbers.includes(phone),
    );

    if (requestWithoutExistentPhones.length === 0) {
      throw new BusinessException(
        'Customer-Phone-Use-Case',
        `All phone numbers already have a verification request: ${existentPhoneNumbers.join(', ')}`,
        BusinessExceptionStatus.ITEM_ALREADY_EXISTS,
      );
    }

    const createdPhonesVerification = await Promise.all(
      requestWithoutExistentPhones.map(phoneNumber =>
        this.customerVerificationAdapter.create({
          id: uuid(),
          customerId: phoneVerificationDto.customerId,
          phoneNumber,
          verificationStatus: PhoneVerificationStatus.PENDING,
          status: RecordStatus.ACTIVE,
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
      ),
    );

    await Promise.all(
      createdPhonesVerification.map(phoneVerification =>
        this.checkNumberAdapter.checkNumber(phoneVerification.phoneNumber),
      ),
    );

    return createdPhonesVerification.map(phoneVerification => ({
      id: phoneVerification.id,
      phoneNumber: phoneVerification.phoneNumber,
    }));
  }

  async getPhoneVerification(phoneNumber: string): Promise<PhoneVerificationResponseDto> {
    logger.info(`Fetching phone by phone number: ${phoneNumber}`);

    const [phoneVerification] = await this.customerVerificationAdapter.getAll(
      { phoneNumber },
      {
        phoneNumber: true,
        verificationStatus: true,
      },
    );

    if (!phoneVerification) {
      throw new BusinessException(
        'Customer-Phone-Use-Case',
        `Phone verification not found for phone number: ${phoneNumber}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return this.getPhoneVerificationResponseDto(phoneVerification);
  }

  async checkPendingVerifications(): Promise<void> {
    logger.info(`Checking pending phone verifications..`);

    const pendingVerifications = await this.customerVerificationAdapter.getAll({
      verificationStatus: PhoneVerificationStatus.PENDING,
      status: RecordStatus.ACTIVE,
    });

    for (const verification of pendingVerifications) {
      const checkResult = await this.checkNumberAdapter.checkNumber(verification.phoneNumber);
      const verificationResult = checkResult?.message?.whatsapp;
      const newStatus =
        verificationResult == 'yes'
          ? PhoneVerificationStatus.CONFIRMED
          : verificationResult == 'no'
          ? PhoneVerificationStatus.NOT_EXISTS
          : PhoneVerificationStatus.PENDING;

      verification.verificationStatus = newStatus;
      verification.updatedAt = new Date();

      await this.customerVerificationAdapter.update(verification);

      logger.info(`Phone verification for ${verification.phoneNumber} updated to ${newStatus}`);
    }
  }

  private getPhoneVerificationResponseDto(
    phoneVerification: PhoneVerificationEntity,
  ): PhoneVerificationResponseDto {
    return plainToClass(PhoneVerificationResponseDto, phoneVerification);
  }
}
