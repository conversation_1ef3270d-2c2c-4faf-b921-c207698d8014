import { Body, Controller, Get, Param, Post, Version } from '@nestjs/common';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { PhoneVerificationDto } from '@message-hub/application/dto/in/phone-verification.dto';
import { PhoneVerificationUseCase } from '@message-hub/application/use-cases/phone-verification.use-case';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { PhoneVerificationResponseDto } from '@message-hub/application/dto/out/phone-verification-response.dto';

@ApiTags('Phone Verification')
@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('message-hub/verify/phones')
export class PhoneVerificationController {
  constructor(private readonly verificationPhoneUseCase: PhoneVerificationUseCase) {}

  @Post()
  @Version('1')
  @ApiOperation({ summary: 'Create phone verification requests' })
  @ApiBody({ type: PhoneVerificationDto, description: 'Phone verification request payload' })
  @ApiResponse({ status: 201, description: 'Phone verification requests created successfully', type: [PhoneVerificationResponseDto] })
  @ApiResponse({ status: 400, description: 'Bad Request - Validation error or phone numbers already have a verification request' })
  async createPhoneVerification(@Body() phoneVerificationDto: PhoneVerificationDto): Promise<any> {
    const createdVerifications = await this.verificationPhoneUseCase.createPhonesVerification(
      phoneVerificationDto,
    );

    return {
      statusCode: 201,
      data: createdVerifications,
    };
  }

  @Get('/:phoneNumber')
  @Version('1')
  @ApiOperation({ summary: 'Retrieve phone verification status by phone number' })
  @ApiParam({ name: 'phoneNumber', description: 'The phone number to retrieve verification status for', type: String })
  @ApiResponse({ status: 200, description: 'Phone verification status retrieved successfully', type: PhoneVerificationResponseDto })
  @ApiResponse({ status: 404, description: 'Not Found - Phone verification not found for the given phone number' })
  async findByPhoneNumber(@Param('phoneNumber') phoneNumber: string): Promise<any> {
    const phoneVerification = await this.verificationPhoneUseCase.getPhoneVerification(phoneNumber);

    return {
      statusCode: 200,
      data: phoneVerification,
    };
  }

  @Post('/check')
  @Version('1')
  @ApiOperation({ summary: 'Trigger a check for pending phone verifications' })
  @ApiResponse({ status: 201, description: 'Pending phone verifications check initiated successfully' })
  async checkNumbers(): Promise<any> {
    await this.verificationPhoneUseCase.checkPendingVerifications();
    return {
      statusCode: 201,
    };
  }
}