import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { PhoneVerificationEntity } from '@message-hub/domain/entities/phone-verification.entity';
import { PhoneVerificationPort } from '@message-hub/infrastructure/ports/db/phone-verification.port';

@Injectable()
export class PhoneVerificationAdapter
  extends PrismaCommonAdapter<PhoneVerificationEntity>
  implements PhoneVerificationPort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'phoneVerification');
  }
}
