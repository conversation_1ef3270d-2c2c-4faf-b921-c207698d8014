import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { logger } from '@edutalent/commons-sdk';
import { lastValueFrom } from 'rxjs';
import { handleHttpError } from '@common/utils/handle-http-error';
import { InfraCheckNumberPort } from '@message-hub/infrastructure/ports/http/check-number.port';

@Injectable()
export class InfraCheckNumberAdapter implements InfraCheckNumberPort {
  constructor(private readonly httpService: HttpService) {}

  async checkNumber(phoneNumber: string): Promise<any> {
    try {
      const url = `${process.env.CHECK_NUMBER_SERVICE_URL}/v1/whatsapp`;
      logger.info(
        `Posting data to ${url} to start phone number verification. Phone number: ${phoneNumber}`,
      );

      const headers = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-API-KEY': process.env.CHECK_NUMBER_API_KEY,
      };
      const verifyNumberDto = {
        number: `+${phoneNumber}`,
        country: 'BR',
      };

      const checkNumberResponse = await lastValueFrom(
        this.httpService.post(url, new URLSearchParams(verifyNumberDto).toString(), { headers }),
      );

      return checkNumberResponse.data;
    } catch (error) {
      logger.error(
        `Error verifying phone number in check number ${phoneNumber} api: ${JSON.stringify(error)}`,
      );
      handleHttpError(error, 'Infra-CheckNumber-adapter');
    }
  }
}
