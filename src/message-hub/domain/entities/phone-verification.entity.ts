import { PhoneVerificationStatus, RecordStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class PhoneVerificationEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsString()
  @IsNotEmpty()
  readonly phoneNumber: string;

  @IsEnum(PhoneVerificationStatus)
  verificationStatus: PhoneVerificationStatus;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt: Date;

  @IsDate()
  @IsNotEmpty()
  updatedAt: Date;

  constructor(
    id: string,
    customerId: string,
    phoneNumber: string,
    verificationStatus: PhoneVerificationStatus,
    createdAt: Date,
    updatedAt: Date,
  ) {
    this.id = id;
    this.customerId = customerId;
    this.phoneNumber = phoneNumber;
    this.verificationStatus = verificationStatus;
    this.createdAt = createdAt|| new Date();
    this.updatedAt = updatedAt || new Date();
  }
}
