import { Test, TestingModule } from '@nestjs/testing';
import { IntegrationUseCase } from '@integration-hub/application/use-cases/integration.use-case';
import { IntegrationStrategyManager } from '@integration-hub/domain/services/integration-strategy.manager';
import { IntegrationStrategyFactory } from '@integration-hub/domain/factories/integration-strategy.factory';
import { RecuperaIntegrationStrategy } from '@integration-hub/domain/strategies/recupera-integration.strategy';
import { PortfolioItemUpdatedRequestDTO } from '@integration-hub/application/dto/in/portfolio-item-updated-request.dto';
import { BusinessException } from '@common/exception/types/BusinessException';
import { HttpService } from '@nestjs/axios';

describe('IntegrationUseCase', () => {
  let useCase: IntegrationUseCase;
  let strategyManager: IntegrationStrategyManager;

  beforeEach(async () => {
    const mockHttpService = {
      post: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IntegrationUseCase,
        IntegrationStrategyManager,
        IntegrationStrategyFactory,
        RecuperaIntegrationStrategy,
        {
          provide: 'BusinessBasePort',
          useValue: {
            sendDirectMessage: jest.fn(),
            getPortfolioItem: jest.fn(),
          },
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    useCase = module.get<IntegrationUseCase>(IntegrationUseCase);
    strategyManager = module.get<IntegrationStrategyManager>(IntegrationStrategyManager);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should execute integration strategy successfully', async () => {
    const integrationConfig = {
      providerName: 'Recupera',
      credentials: {
        usuario: 'usuario',
        senha: 'senha',
        empresa: 'empresa',
        chaveAtivacao: 'chaveAtivacao',
      },
    };

    const request: PortfolioItemUpdatedRequestDTO = {
      customerId: 'customer-id',
      portfolioItemId: 'portfolio-item-id',
      portfolioId: 'portfolio-id',
      workflowId: 'workflow-id',
      currentStatus: 'FINISHED',
      metadata: {
        integrationConfig,
      },
    };

    // Mock the strategy execution to avoid actual HTTP calls
    const mockResult = {
      success: true,
      strategyName: 'Recupera',
      message: 'Integration completed successfully',
      data: { portfolioItemId: 'portfolio-item-id' },
      executedAt: new Date(),
      hasData: () => true,
    };
    jest.spyOn(strategyManager, 'executeStrategy').mockResolvedValue(mockResult as any);

    const result = await useCase.executeIntegrationStrategy(request);

    expect(result).toBeDefined();
    expect(result.success).toBe(true);
    expect(result.strategyName).toBe('Recupera');
  });

  it('should throw exception when integration config is missing', async () => {
    const request: PortfolioItemUpdatedRequestDTO = {
      customerId: 'customer-id',
      portfolioItemId: 'portfolio-item-id',
      portfolioId: 'portfolio-id',
      workflowId: 'workflow-id',
      currentStatus: 'FINISHED',
      metadata: undefined, // No metadata means no integrationConfig
    };

    try {
      await useCase.executeIntegrationStrategy(request);
      fail('Expected BusinessException to be thrown');
    } catch (error) {
      expect(error).toBeInstanceOf(BusinessException);
      expect(error.message).toBe('No integration config found');
    }
  });

  it('should throw exception when integration config is missing from metadata', async () => {
    const request: PortfolioItemUpdatedRequestDTO = {
      customerId: 'customer-id',
      portfolioItemId: 'portfolio-item-id',
      portfolioId: 'portfolio-id',
      workflowId: 'workflow-id',
      currentStatus: 'FINISHED',
      metadata: {
        // metadata exists but no integrationConfig
        someOtherData: 'value',
      },
    };

    try {
      await useCase.executeIntegrationStrategy(request);
      fail('Expected BusinessException to be thrown');
    } catch (error) {
      expect(error).toBeInstanceOf(BusinessException);
      expect(error.message).toBe('No integration config found');
    }
  });
});
