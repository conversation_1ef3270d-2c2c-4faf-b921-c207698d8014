import { Injectable } from '@nestjs/common';
import { PortfolioItemUpdatedRequestDTO } from '@integration-hub/application/dto/in/portfolio-item-updated-request.dto';
import { BusinessException, BusinessExceptionStatus } from '@common/exception/types/BusinessException';
import { IntegrationContext } from '@integration-hub/domain/entities/integration-context.entity';
import { IntegrationStrategyManager } from '@integration-hub/domain/services/integration-strategy.manager';
import { IntegrationResult } from '@integration-hub/domain/entities/integration-result.entity';

@Injectable()
export class IntegrationUseCase {
  constructor(
    private readonly integrationStrategyManager: IntegrationStrategyManager
  ) {}

  async executeIntegrationStrategy(portfolioItemUpdatedRequestDTO: PortfolioItemUpdatedRequestDTO): Promise<IntegrationResult> {
    const integrationConfig = portfolioItemUpdatedRequestDTO.metadata?.integrationConfig;

    if (!integrationConfig) {
      throw new BusinessException(
        'IntegrationUseCase',
        'No integration config found',
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const integrationContext = new IntegrationContext(
      portfolioItemUpdatedRequestDTO.customerId,
      portfolioItemUpdatedRequestDTO.portfolioItemId,
      portfolioItemUpdatedRequestDTO.portfolioId,
      portfolioItemUpdatedRequestDTO.workflowId,
      portfolioItemUpdatedRequestDTO.currentStatus,
      integrationConfig,
      portfolioItemUpdatedRequestDTO.metadata,
    );

    const result = await this.integrationStrategyManager.executeStrategy(integrationContext);
    return result;
  }
}