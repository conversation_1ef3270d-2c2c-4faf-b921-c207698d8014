import { Test, TestingModule } from '@nestjs/testing';
import { RecuperaIntegrationStrategy } from '@integration-hub/domain/strategies/recupera-integration.strategy';
import { IntegrationContext } from '@integration-hub/domain/entities/integration-context.entity';
import { HttpService } from '@nestjs/axios';

describe('RecuperaIntegrationStrategy', () => {
  let strategy: RecuperaIntegrationStrategy;

  beforeEach(async () => {
    const mockHttpService = {
      post: jest.fn(),
    };

    const mockBusinessBasePort = {
      sendDirectMessage: jest.fn(),
      getPortfolioItem: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RecuperaIntegrationStrategy,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: 'BusinessBasePort',
          useValue: mockBusinessBasePort,
        },
      ],
    }).compile();

    strategy = module.get<RecuperaIntegrationStrategy>(RecuperaIntegrationStrategy);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  it('should return correct strategy name', () => {
    expect(strategy.getStrategyName()).toBe('Recupera');
  });

  it('should handle context with Recupera provider', () => {
  
    const integrationConfig = {
      providerName: 'Recupera',
    };

    const context = new IntegrationContext(
      'customer-id',
      'portfolio-item-id',
      'portfolio-id',
      'workflow-id',
      'FINISHED',
      integrationConfig,
    );

    expect(strategy.canHandle(context)).toBe(true);
  });

  it('should not handle context with different provider', () => {
    const integrationConfig = {
      providerName: 'OtherProvider',
    };

    const context = new IntegrationContext(
      'customer-id',
      'portfolio-item-id',
      'portfolio-id',
      'workflow-id',
      'FINISHED',
      integrationConfig,
    );

    expect(strategy.canHandle(context)).toBe(false);
  });

});
