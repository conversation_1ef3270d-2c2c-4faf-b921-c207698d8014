import { IntegrationHubPort } from "@business-base/infrastructure/ports/http/integration-hub.port";
import { logger } from "@edutalent/commons-sdk";
import { HttpService } from "@nestjs/axios";
import { Injectable } from "@nestjs/common";
import { lastValueFrom } from "rxjs";
import { BusinessException, BusinessExceptionStatus } from "@common/exception/types/BusinessException";

@Injectable()
export class IntegrationHubAdapter implements IntegrationHubPort {
  private readonly integrationHubServiceUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.integrationHubServiceUrl = process.env.INTEGRATION_HUB_SERVICE_URL.toString();
  }
  
  async executeIntegrationStrategy(context: any): Promise<any> {
    try{
      const url = `${this.integrationHubServiceUrl}/api/v1/integration-hub/execute`;
      logger.info(`Posting data to: ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const response = await lastValueFrom(this.httpService.post(url, context, { headers }));
      return response.data;
    } catch (error) {
      logger.error('Failed to execute integration strategy', error);
      throw new BusinessException(
        'IntegrationHubAdapter',
        `Failed to execute integration strategy with error: ${JSON.stringify(error.response.data)}`,
        BusinessExceptionStatus.GENERAL_ERROR,
      );
    }
  }
}
