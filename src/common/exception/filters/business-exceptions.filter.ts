// business.exception.filter.ts
import { Catch, ExceptionFilter, ArgumentsHost, HttpStatus } from '@nestjs/common';
import { BaseErrorResponse } from '@common/exception/types/BaseErrorResponse';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { logger } from '@edutalent/commons-sdk';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';

@Catch(BusinessException)
export class BusinessExceptionFilter implements ExceptionFilter {
  catch(exception: BusinessException, host: ArgumentsHost) {
    const mappedCode = (() => {
      switch (exception.status) {
        case BusinessExceptionStatus.ITEM_NOT_FOUND:
          return HttpStatus.NOT_FOUND;
        case BusinessExceptionStatus.INVALID_INPUT:
          return HttpStatus.BAD_REQUEST;
        case BusinessExceptionStatus.ITEM_ALREADY_EXISTS:
          return HttpStatus.CONFLICT;
        default:
          return HttpStatus.INTERNAL_SERVER_ERROR;
      }
    })();

    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    const traceId = CorrelationContextService.getTraceId();
    const context = CorrelationContextService.getContext();
    const status = mappedCode || HttpStatus.INTERNAL_SERVER_ERROR;
    const errorResponse = {
      message: exception.message,
      error: exception.stack,
    };

    const message =
      typeof errorResponse === 'string'
        ? errorResponse
        : (errorResponse as any).message || exception.message;

    logger.error('Business logic exception occurred', {
      traceId,
      message,
      origin: 'BusinessException',
      path: request.url,
      method: request.method,
      statusCode: status,
      error: exception.message,
      errorType: 'BUSINESS_LOGIC_ERROR',
      severity: 'MEDIUM',
      businessExceptionStatus: exception.status,
      businessContext: context?.operation,
      timestamp: new Date().toISOString(),
      layer: 'BUSINESS_EXCEPTION_FILTER',
    });

    const messages = Array.isArray(message) ? message : [message];

    const payload: BaseErrorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      message: messages,
      data: { path: request.url, traceId },
    };

    return response.status(status).json(payload);
  }
}
