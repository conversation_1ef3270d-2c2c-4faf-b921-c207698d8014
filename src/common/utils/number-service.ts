import { Injectable } from '@nestjs/common';

@Injectable()
export class NumberService {
  stringToNumber(input: string): number {
    const number = Number(input);
    if (isNaN(number) || !input?.trim()) {
      throw new Error(`Invalid input: '${input}' is not a valid number.`);
    }
    return number;
  }

  getRandomNumber(max: number): number {
    if (max == 0) {
      return 0;
    }

    if (max <= 60) {
      throw new Error(`Invalid maximum value: max (${max}) must be greater than 60.`);
    }
    return Math.floor(Math.random() * (max - 60 + 1)) + 60;
  }
}
