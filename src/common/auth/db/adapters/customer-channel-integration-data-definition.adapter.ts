import { Injectable } from '@nestjs/common';
import { DynamoDBDocumentClient, GetCommand, PutCommand } from '@aws-sdk/lib-dynamodb';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { DynamoException } from '@common/exception/types/DynamoException';

import { logger } from '@edutalent/commons-sdk';
import { CustomerChannelIntegrationDataDefinitionPort } from '@common/auth/db/ports/customer-channel-integration-data-definition.port';
import { CustomerChannelIntegrationDataDefinitionEntity } from '@common/auth/entities/customer-channel-integration-data-definition.entity';

@Injectable()
export class CustomerChannelIntegrationDataDefinitionAdapter
  implements CustomerChannelIntegrationDataDefinitionPort
{
  private readonly dynamoClient: DynamoDBDocumentClient;
  private readonly DYNAMO_TABLE_NAME = 'transcendence_customer_channel_integration_data';

  constructor(private readonly dynamoService: DynamoService) {
    this.dynamoClient = this.dynamoService.dynamoClient;
  }

  async create(
    entity: CustomerChannelIntegrationDataDefinitionEntity,
  ): Promise<CustomerChannelIntegrationDataDefinitionEntity> {
    try {
      logger.info(
        `Creating customer channel integration data definition for id: ${JSON.stringify(
          entity,
        )}. \n Data: ${JSON.stringify(entity)}`,
      );

      await this.dynamoClient.send(
        new PutCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Item: {
            id: entity.id,
            data: entity.data,
          },
        }),
      );
      return entity;
    } catch (error) {
      logger.error(
        `Error saving middleware response output  and portfolio item id: ${JSON.stringify(
          entity,
        )}. \n Error: ${JSON.stringify(error)}`,
      );

      throw new DynamoException({
        message: `Error while saving customer channel integration data definition for id: ${
          entity.id
        }. Error: ${error.message || error}. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      });
    }
  }

  async get(key: string): Promise<CustomerChannelIntegrationDataDefinitionEntity> {
    try {
      const { Item } = await this.dynamoClient.send(
        new GetCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Key: {
            id: key,
          },
        }),
      );

      return Item as CustomerChannelIntegrationDataDefinitionEntity;
    } catch (error) {
      logger.error(
        `Error fetching customer channel integration data definition for key: ${key}. \n Error: ${JSON.stringify(
          error,
        )}`,
      );

      throw new DynamoException({
        message: `Error while fetching customer channel integration data definition for key: ${key}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      });
    }
  }
}
