import { NestFactory } from '@nestjs/core';
import * as dotenv from 'dotenv';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { AppModule } from '@app.module';
import { logger } from '@edutalent/commons-sdk';
import { PrismaExceptionFilter } from '@common/exception/filters/prisma-exceptions.filter';
import { BusinessExceptionFilter } from '@common/exception/filters/business-exceptions.filter';
import { DynamoExceptionFilter } from '@common/exception/filters/dynamo-exceptions.filter';
import { OpenAIExceptionFilter } from '@common/exception/filters/openai-exceptions.filter';
import { AuthExceptionFilter } from '@common/exception/filters/auth-exceptions.filter';
import { SwaggerModule } from '@nestjs/swagger';
import { DocumentBuilder } from '@nestjs/swagger';
import * as express from 'express';
import { WorkflowExceptionFilter } from '@common/exception/filters/workflow-exceptions.filter';

dotenv.config();

// Registra captura global de promessas rejeitadas e exceções não tratadas
process.on('unhandledRejection', (reason, promise) => {
  // eslint-disable-next-line no-console
  console.error('❌ unhandledRejection - Promessa rejeitada sem catch:', reason, promise);
});

process.on('uncaughtException', (error: Error) => {
  // eslint-disable-next-line no-console
  console.error('🔥 uncaughtException - Exceção não tratada:', error);
});

declare global {
  interface BigInt {
    toJSON(): number;
  }
}

BigInt.prototype.toJSON = function () {
  return Number(this);
};

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.setGlobalPrefix('api');
  app.enableVersioning({
    type: VersioningType.URI,
  });

  // Enable CORS for requests from allowed origins
  const allowedOrigins = [
    process.env.INTELLIGENCE_SERVICE_URL,
    process.env.ORCHESTRATOR_SERVICE_URL,
    process.env.BUSINESS_BASE_SERVICE_URL,
    process.env.WHATSAPP_SELFHOSTED_SERVICE_URL,
    process.env.COLLECT_CASH_TRANSCENDENCE_ADMIN_WEB_URL,
    process.env.FIDELEASY_TRANSCENDENCE_ADMIN_WEB_URL,
    process.env.SALESZAP_TRANSCENDENCE_ADMIN_WEB_URL,
  ];

  app.enableCors({
    origin: (origin, callback) => {
      if (
        !origin || // Allow no-origin (for example, for pre-flight requests)
        allowedOrigins.some(allowedOrigin => {
          // If the allowed origin is a wildcard pattern like "*.collect.io", perform a regex match
          if (allowedOrigin.includes('*')) {
            const regex = new RegExp('^' + allowedOrigin.replace('*', '.*') + '$');
            return regex.test(origin);
          }

          return origin === allowedOrigin;
        })
      ) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'), false);
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'sentry-trace',
      'baggage',
      'Accept-Language',
      'x-digai-client-domain',
      'X-Correlation-ID',
      'correlation-id',
      'X-Trace-ID',
      'trace-id',
    ],
  });

  app.useLogger(logger);
  app.useGlobalFilters(
    app.get(AuthExceptionFilter),
    app.get(PrismaExceptionFilter),
    app.get(BusinessExceptionFilter),
    app.get(DynamoExceptionFilter),
    app.get(OpenAIExceptionFilter),
    app.get(WorkflowExceptionFilter),
  );

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: false, // Allow unknown properties for dynamic properties support
      transform: true,
      transformOptions: { enableImplicitConversion: true },
      forbidNonWhitelisted: false, // Allow unknown properties for dynamic properties support
    }),
  );

  // Add SwaggerUI
  if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local') {
    const config = new DocumentBuilder()
      .setTitle('Transcendence')
      .setDescription('Transcendence API Services')
      .setVersion('1.0')
      .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      })
      .build();
    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('docs', app, document);
  }

  app.use(express.json({ limit: '30mb' }));
  app.use(express.urlencoded({ extended: true, limit: '30mb' }));

  await app.listen(process.env.PORT ?? 3000);
}

bootstrap();
