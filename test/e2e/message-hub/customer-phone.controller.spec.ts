import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import { CustomerPhoneResponseDto } from '@message-hub/application/dto/out/customer-phone-response.dto';
import { CommunicationChannel } from '@common/enums';
import { CustomerPhoneDto } from '@message-hub/application/dto/in/customer-phone.dto';

describe('Customer phone controller (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  it('/api/v1/message-hub/customers/:customerId/phones (POST) - Create a customer phone without error', async () => {
    const customerId = uuidv4();
    const response = await request(app.getHttpServer())
      .post(`/api/v1/message-hub/customers/${customerId}/phones`)
      .send({
        phoneNumber: '5511987665722',
        communicationChannel: CommunicationChannel.WHATSAPPSELFHOSTED,
        apiUrl: 'http://localhost:3000',
        incomingCron: '*/2 * * * *',
        outgoingCron: '* * * * * *',
        outgoingMaxDelay: 50,
        weight: 1,
        dailyLimit: 100,
      } as CustomerPhoneDto)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data).toBeDefined();
    expect(response.body.data.customerId).toBeDefined();
    expect(response.body.data.phoneNumber).toBeDefined();
    expect(response.body.data.phoneNumber).toBe('5511987665722');
    expect(response.body.data.communicationChannel).toBe(CommunicationChannel.WHATSAPPSELFHOSTED);
    expect(response.body.data.apiUrl).toBe('http://localhost:3000');
    expect(response.body.data.createdAt).toBeDefined();
    expect(response.body.data.updatedAt).toBeDefined();
  });

  it('/api/v1/message-hub/customers/:customerId/phones (POST) - Create an existing whatsapp phone for a customer BAD REQUEST ', async () => {
    const createdCustomerPhone = await createCustomerPhone(
      uuidv4(),
      '5511987856723',
      CommunicationChannel.WHATSAPPSELFHOSTED,
    );

    const response = await request(app.getHttpServer())
      .post(`/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones`)
      .send({
        customerId: createdCustomerPhone.customerId,
        phoneNumber: createdCustomerPhone.phoneNumber,
        communicationChannel: CommunicationChannel.WHATSAPPSELFHOSTED,
        apiUrl: 'http://localhost:3000',
        incomingCron: '*/2 * * * *',
        outgoingCron: '* * * * * *',
        outgoingMaxDelay: 50,
        weight: 1,
        dailyLimit: 100,
      } as CustomerPhoneDto)
      .expect(400);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(400);
    expect(response.body.message).toBeDefined();
    expect(response.body.message.length).toBe(1);
    expect(response.body.message[0]).toBe('PrismaExceptionP2002 - Esse registro já existe');
  });

  it('/api/v1/message-hub/customers/:customerId/phones/:phoneNumber (GET) - Retrieve a customer phone number without errors ', async () => {
    const createdCustomerPhone = await createCustomerPhone(
      uuidv4(),
      '5511987555724',
      CommunicationChannel.WHATSAPPSELFHOSTED,
    );

    const response = await request(app.getHttpServer())
      .get(
        `/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones/${createdCustomerPhone.phoneNumber}`,
      )
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data[0].customerId).toBe(createdCustomerPhone.customerId);
    expect(response.body.data[0].phoneNumber).toBe(createdCustomerPhone.phoneNumber);
    expect(response.body.data[0].communicationChannel).toBe(
      createdCustomerPhone.communicationChannel,
    );
    expect(response.body.data[0].apiUrl).toBeDefined();
    expect(response.body.data[0].createdAt).toBeDefined();
    expect(response.body.data[0].updatedAt).toBeDefined();
  });

  it('message-hub/customers/:customerId/phones (GET) - Retrieve all customer phones by customer ID', async () => {
    const createdCustomerPhone = await createCustomerPhone(
      uuidv4(),
      '5511987555725',
      CommunicationChannel.SMS_VONAGE,
    );
    const createdCustomerPhone2 = await createCustomerPhone(
      createdCustomerPhone.customerId,
      '5511987555726',
      CommunicationChannel.WHATSAPPSELFHOSTED,
    );

    const response = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones`)
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.length).toBe(2);
    expect(response.body.data[0].customerId).toBe(createdCustomerPhone.customerId);
    expect(response.body.data[0].phoneNumber).toBe(createdCustomerPhone.phoneNumber);
    expect(response.body.data[0].apiUrl).toBeDefined();
    expect(response.body.data[0].communicationChannel).toBe(CommunicationChannel.SMS_VONAGE);
    expect(response.body.data[0].createdAt).toBeDefined();
    expect(response.body.data[0].updatedAt).toBeDefined();
    expect(response.body.data[1].customerId).toBe(createdCustomerPhone2.customerId);
    expect(response.body.data[1].phoneNumber).toBe(createdCustomerPhone2.phoneNumber);
    expect(response.body.data[1].communicationChannel).toBe(
      CommunicationChannel.WHATSAPPSELFHOSTED,
    );
    expect(response.body.data[1].apiUrl).toBeDefined();
    expect(response.body.data[1].createdAt).toBeDefined();
    expect(response.body.data[1].updatedAt).toBeDefined();
  });

  it('/api/v1/message-hub/customers/:customerId/phones/:phoneNumber/api-url (PUT) - Update apiUrl by customer ID and phone Number', async () => {
    const createdCustomerPhone = await createCustomerPhone(
      uuidv4(),
      '5511987555727',
      CommunicationChannel.WHATSAPPSELFHOSTED,
    );

    const response = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones`)
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.length).toBe(1);
    expect(response.body.data[0].customerId).toBe(createdCustomerPhone.customerId);
    expect(response.body.data[0].phoneNumber).toBe(createdCustomerPhone.phoneNumber);
    expect(response.body.data[0].apiUrl).toBeDefined();
    expect(response.body.data[0].communicationChannel).toBe(
      CommunicationChannel.WHATSAPPSELFHOSTED,
    );
    expect(response.body.data[0].createdAt).toBeDefined();
    expect(response.body.data[0].updatedAt).toBeDefined();

    const responsePut = await request(app.getHttpServer())
      .put(
        `/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones/${createdCustomerPhone.phoneNumber}/api-url`,
      )
      .send({
        apiUrl: 'http://localhost:3000',
        communicationChannel: CommunicationChannel.WHATSAPPSELFHOSTED,
      })
      .expect(200);

    expect(responsePut.body).toBeDefined();
    expect(responsePut.body.statusCode).toBe(200);
    expect(responsePut.body.data).toBeDefined();
    expect(responsePut.body.data.customerId).toBe(createdCustomerPhone.customerId);
    expect(responsePut.body.data.phoneNumber).toBe(createdCustomerPhone.phoneNumber);
    expect(responsePut.body.data.communicationChannel).toBe(
      createdCustomerPhone.communicationChannel,
    );
    expect(responsePut.body.data.apiUrl).toBe('http://localhost:3000');
    expect(responsePut.body.data.createdAt).toBeDefined();
    expect(responsePut.body.data.updatedAt).toBeDefined();

    const responseGet = await request(app.getHttpServer())
      .get(
        `/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones/${createdCustomerPhone.phoneNumber}`,
      )
      .expect(200);

    expect(responseGet.body).toBeDefined();
    expect(responseGet.body.statusCode).toBe(200);
    expect(responseGet.body.data).toBeDefined();
    expect(responseGet.body.data[0].customerId).toBe(createdCustomerPhone.customerId);
    expect(responseGet.body.data[0].phoneNumber).toBe(createdCustomerPhone.phoneNumber);
    expect(responseGet.body.data[0].communicationChannel).toBe(
      createdCustomerPhone.communicationChannel,
    );
    expect(responseGet.body.data[0].apiUrl).toBe('http://localhost:3000');
    expect(responseGet.body.data[0].createdAt).toBeDefined();
    expect(responseGet.body.data[0].updatedAt).toBeDefined();
  });

  it('/api/v1/message-hub/customers/:customerId/phones (DELETE) - Delete a existing customer phone by customer id and phone number without errors ', async () => {
    const createdCustomerPhone = await createCustomerPhone(
      uuidv4(),
      '5511987555730',
      CommunicationChannel.WHATSAPPSELFHOSTED,
    );
    const createdCustomerPhone2 = await createCustomerPhone(
      createdCustomerPhone.customerId,
      '5511987555731',
      CommunicationChannel.WHATSAPPSELFHOSTED,
    );

    const response = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones`)
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.length).toBe(2);
    expect(response.body.data[0].customerId).toBe(createdCustomerPhone.customerId);
    expect(response.body.data[0].phoneNumber).toBe(createdCustomerPhone.phoneNumber);
    expect(response.body.data[0].communicationChannel).toBe(
      createdCustomerPhone.communicationChannel,
    );
    expect(response.body.data[0].apiUrl).toBeDefined();
    expect(response.body.data[0].createdAt).toBeDefined();
    expect(response.body.data[0].updatedAt).toBeDefined();
    expect(response.body.data[1].customerId).toBe(createdCustomerPhone2.customerId);
    expect(response.body.data[1].phoneNumber).toBe(createdCustomerPhone2.phoneNumber);
    expect(response.body.data[1].communicationChannel).toBe(
      createdCustomerPhone2.communicationChannel,
    );
    expect(response.body.data[1].apiUrl).toBeDefined();
    expect(response.body.data[1].createdAt).toBeDefined();
    expect(response.body.data[1].updatedAt).toBeDefined();

    await request(app.getHttpServer())
      .delete(`/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones`)
      .expect(200);

    const responseGet = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones`)
      .expect(200);

    expect(responseGet.body).toBeDefined();
    expect(responseGet.body.statusCode).toBe(200);
    expect(responseGet.body.data).toBeDefined();
    expect(responseGet.body.data.length).toBe(0);
  });

  it('/v1/message-hub/customers/:customerId/phones (DELETE) - Delete a existing customer phones by customer id and phone number without errors ', async () => {
    const createdCustomerPhone = await createCustomerPhone(uuidv4(), '5511987555728');
    const createdCustomerPhone2 = await createCustomerPhone(
      createdCustomerPhone.customerId,
      '5511987555729',
    );

    const response = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones/`)
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.length).toBe(2);
    expect(response.body.data[0].customerId).toBe(createdCustomerPhone.customerId);
    expect(response.body.data[0].phoneNumber).toBe(createdCustomerPhone.phoneNumber);
    expect(response.body.data[0].communicationChannel).toBe(
      createdCustomerPhone.communicationChannel,
    );
    expect(response.body.data[0].apiUrl).toBeDefined();
    expect(response.body.data[0].createdAt).toBeDefined();
    expect(response.body.data[0].updatedAt).toBeDefined();
    expect(response.body.data[1].customerId).toBe(createdCustomerPhone2.customerId);
    expect(response.body.data[1].phoneNumber).toBe(createdCustomerPhone2.phoneNumber);
    expect(response.body.data[1].communicationChannel).toBe(
      createdCustomerPhone2.communicationChannel,
    );
    expect(response.body.data[1].apiUrl).toBeDefined();
    expect(response.body.data[1].createdAt).toBeDefined();
    expect(response.body.data[1].updatedAt).toBeDefined();

    await request(app.getHttpServer())
      .delete(
        `/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones/${createdCustomerPhone.phoneNumber}`,
      )
      .expect(200);

    const responseGet = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones`)
      .expect(200);

    expect(responseGet.body).toBeDefined();
    expect(responseGet.body.statusCode).toBe(200);
    expect(responseGet.body.data).toBeDefined();
    expect(responseGet.body.data.length).toBe(1);
  });

  it('/v1/message-hub/customers/:customerId/phones (DELETE) - Delete a customer phone and verify weight rebalancing', async () => {
    const customerId = uuidv4();
    const phone1 = await createCustomerPhone(
      customerId,
      '5511987665728',
      CommunicationChannel.WHATSAPPSELFHOSTED,
      40,
    );
    const phone2 = await createCustomerPhone(
      customerId,
      '5511987665729',
      CommunicationChannel.WHATSAPPSELFHOSTED,
      30,
    );
    const phone3 = await createCustomerPhone(
      customerId,
      '5511987665730',
      CommunicationChannel.WHATSAPPSELFHOSTED,
      30,
    );

    const initialResponse = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/customers/${customerId}/phones`)
      .expect(200);

    expect(initialResponse.body).toBeDefined();
    expect(initialResponse.body.statusCode).toBe(200);
    expect(initialResponse.body.data).toBeDefined();
    expect(initialResponse.body.data.length).toBe(3);
    expect(initialResponse.body.data).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          customerId,
          phoneNumber: phone1.phoneNumber,
          weight: 40,
          communicationChannel: phone1.communicationChannel,
          apiUrl: expect.any(String),
          createdAt: expect.any(String),
          updatedAt: expect.any(String),
        }),
        expect.objectContaining({
          customerId,
          phoneNumber: phone2.phoneNumber,
          weight: 30,
          communicationChannel: phone2.communicationChannel,
          apiUrl: expect.any(String),
          createdAt: expect.any(String),
          updatedAt: expect.any(String),
        }),
        expect.objectContaining({
          customerId,
          phoneNumber: phone3.phoneNumber,
          weight: 30,
          communicationChannel: phone3.communicationChannel,
          apiUrl: expect.any(String),
          createdAt: expect.any(String),
          updatedAt: expect.any(String),
        }),
      ]),
    );

    await request(app.getHttpServer())
      .delete(`/api/v1/message-hub/customers/${customerId}/phones/${phone3.phoneNumber}`)
      .expect(200);

    const responseAfterDelete = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/customers/${customerId}/phones`)
      .expect(200);

    expect(responseAfterDelete.body).toBeDefined();
    expect(responseAfterDelete.body.statusCode).toBe(200);
    expect(responseAfterDelete.body.data).toBeDefined();
    expect(responseAfterDelete.body.data.length).toBe(2);

    const remainingPhones = responseAfterDelete.body.data;
    const weights = remainingPhones.map(phone => phone.weight);
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

    expect(weights.every(weight => Number.isInteger(weight))).toBe(true);
    expect(totalWeight).toBe(100);

    expect(remainingPhones).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          customerId,
          phoneNumber: phone1.phoneNumber,
          weight: expect.any(Number),
          communicationChannel: phone1.communicationChannel,
        }),
        expect.objectContaining({
          customerId,
          phoneNumber: phone2.phoneNumber,
          weight: expect.any(Number),
          communicationChannel: phone2.communicationChannel,
        }),
      ]),
    );

    const phone1After = remainingPhones.find(p => p.phoneNumber === phone1.phoneNumber);
    const phone2After = remainingPhones.find(p => p.phoneNumber === phone2.phoneNumber);
    expect(phone1After.weight).toBeGreaterThanOrEqual(57);
    expect(phone1After.weight).toBeLessThanOrEqual(58);
    expect(phone2After.weight).toBeGreaterThanOrEqual(42);
    expect(phone2After.weight).toBeLessThanOrEqual(43);
  });

  it('/v1/message-hub/customers/:customerId/phones (DELETE) - Delete all existing customer phones by customer id without errors ', async () => {
    const createdCustomerPhone = await createCustomerPhone(uuidv4(), '5511987555740');
    const createdCustomerPhone2 = await createCustomerPhone(
      createdCustomerPhone.customerId,
      '5511987555741',
    );

    const response = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones/`)
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.length).toBe(2);
    expect(response.body.data[0].customerId).toBe(createdCustomerPhone.customerId);
    expect(response.body.data[0].phoneNumber).toBe(createdCustomerPhone.phoneNumber);
    expect(response.body.data[0].communicationChannel).toBe(
      createdCustomerPhone.communicationChannel,
    );
    expect(response.body.data[0].apiUrl).toBeDefined();
    expect(response.body.data[0].createdAt).toBeDefined();
    expect(response.body.data[0].updatedAt).toBeDefined();
    expect(response.body.data[1].customerId).toBe(createdCustomerPhone2.customerId);
    expect(response.body.data[1].phoneNumber).toBe(createdCustomerPhone2.phoneNumber);
    expect(response.body.data[1].communicationChannel).toBe(
      createdCustomerPhone.communicationChannel,
    );
    expect(response.body.data[1].apiUrl).toBeDefined();
    expect(response.body.data[1].createdAt).toBeDefined();
    expect(response.body.data[1].updatedAt).toBeDefined();

    await request(app.getHttpServer())
      .delete(`/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones`)
      .expect(200);

    const responseGet = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/customers/${createdCustomerPhone.customerId}/phones`)
      .expect(200);

    expect(responseGet.body).toBeDefined();
    expect(responseGet.body.statusCode).toBe(200);
    expect(responseGet.body.data).toBeDefined();
    expect(responseGet.body.data.length).toBe(0);
  });

  it('/api/v1/message-hub/phones/:phoneNumber/communication-channel/:communicationChannel (GET) - Retrieve a customer phone by phone number and communication channel without errors ', async () => {
    const createdCustomerPhone = await createCustomerPhone(uuidv4(), '5511987555732');

    const response = await request(app.getHttpServer())
      .get(
        `/api/v1/message-hub/phones/${createdCustomerPhone.phoneNumber}/communication-channel/${createdCustomerPhone.communicationChannel}`,
      )
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.customerId).toBe(createdCustomerPhone.customerId);
    expect(response.body.data.phoneNumber).toBe(createdCustomerPhone.phoneNumber);
    expect(response.body.data.communicationChannel).toBe(createdCustomerPhone.communicationChannel);
    expect(response.body.data.apiUrl).toBe('http://localhost:3000');
    expect(response.body.data.createdAt).toBeDefined();
    expect(response.body.data.updatedAt).toBeDefined();
  });

  async function createCustomerPhone(
    customerId: string,
    phoneNumber?: string,
    communicationChannel?: CommunicationChannel,
    weight?: number,
  ): Promise<CustomerPhoneResponseDto> {
    const response = await request(app.getHttpServer())
      .post(`/api/v1/message-hub/customers/${customerId}/phones`)
      .send({
        phoneNumber: phoneNumber || '5511987555722',
        communicationChannel: communicationChannel || CommunicationChannel.WHATSAPPSELFHOSTED,
        apiUrl: 'http://localhost:3000',
        incomingCron: '*/2 * * * *',
        outgoingCron: '* * * * * *',
        outgoingMaxDelay: 50,
        weight: weight || 1,
        dailyLimit: 100,
      } as CustomerPhoneDto)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data).toBeDefined();
    expect(response.body.data.customerId).toBe(customerId || response.body.data.customerId);
    expect(response.body.data.phoneNumber).toBeDefined();
    expect(response.body.data.phoneNumber).toBe(phoneNumber || '5511987555722');
    expect(response.body.data.communicationChannel).toBe(
      communicationChannel || CommunicationChannel.WHATSAPPSELFHOSTED,
    );
    expect(response.body.data.createdAt).toBeDefined();
    expect(response.body.data.updatedAt).toBeDefined();

    return response.body.data;
  }
});
