import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import { PhoneVerificationDto } from '@message-hub/application/dto/in/phone-verification.dto';
import { PhoneVerificationStatus } from '@common/enums';

describe('Phone verification controller (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  it('/api/v1/message-hub/verify/phones (POST) - Create phone verification without error', async () => {
    const customerId = uuidv4();
    const phoneNumbers = ['5511987555722', '5511916741742'];
    const response = await request(app.getHttpServer())
      .post('/api/v1/message-hub/verify/phones')
      .send({
        customerId,
        phoneNumbers,
      } as PhoneVerificationDto)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(201);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.length).toBe(2);
    expect(response.body.data[0].id).toBeDefined();
    expect(response.body.data[0].phoneNumber).toBe(phoneNumbers[0]);
    expect(response.body.data[1].id).toBeDefined();
    expect(response.body.data[1].phoneNumber).toBe(phoneNumbers[1]);
  });

  it('/api/v1/message-hub/verify/phones (POST) - Create phone verification with existing phone numbers should return BusinessException', async () => {
    const customerId = uuidv4();
    const phoneNumbers = ['***********', '***********'];

    await request(app.getHttpServer())
      .post('/api/v1/message-hub/verify/phones')
      .send({
        customerId,
        phoneNumbers,
      } as PhoneVerificationDto)
      .expect(201);

    const response = await request(app.getHttpServer())
      .post('/api/v1/message-hub/verify/phones')
      .send({
        customerId,
        phoneNumbers,
      } as PhoneVerificationDto)
      .expect(409);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(409);
    expect(response.body.message).toBeDefined();
    expect(response.body.message[0]).toContain(
      'All phone numbers already have a verification request',
    );
  });

  it('/api/v1/message-hub/verify/phones/:phoneNumber (GET) - Retrieve phone verification by phone number without errors', async () => {
    const customerId = uuidv4();
    const phoneNumber = '5511999888888';

    await request(app.getHttpServer())
      .post('/api/v1/message-hub/verify/phones')
      .send({
        customerId,
        phoneNumbers: [phoneNumber],
      } as PhoneVerificationDto)
      .expect(201);

    const response = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/verify/phones/${phoneNumber}`)
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.phoneNumber).toBe(phoneNumber);
    expect(response.body.data.verificationStatus).toBe(PhoneVerificationStatus.PENDING);
  });

  it('/api/v1/message-hub/verify/phones/:phoneNumber (GET) - Retrieve non-existent phone verification should return 404', async () => {
    const nonExistentPhoneNumber = '************';
    const response = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/verify/phones/${nonExistentPhoneNumber}`)
      .expect(404);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(404);
    expect(response.body.message).toBeDefined();
    expect(response.body.message[0]).toContain('Phone verification not found for phone number');
  });

  it('/api/v1/message-hub/verify/phones/check (POST) - Check pending verifications without error', async () => {
    const customerId = uuidv4();
    const phoneNumberToConfirm = '5511987555722';
    const phoneNumberToNotExist = '***********99';

    await request(app.getHttpServer())
      .post('/api/v1/message-hub/verify/phones')
      .send({
        customerId,
        phoneNumbers: [phoneNumberToConfirm, phoneNumberToNotExist],
      } as PhoneVerificationDto)
      .expect(201);

    const response = await request(app.getHttpServer())
      .post('/api/v1/message-hub/verify/phones/check')
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(201);

    const confirmedResponse = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/verify/phones/${phoneNumberToConfirm}`)
      .expect(200);
    expect(confirmedResponse.body.data.verificationStatus).toBe(PhoneVerificationStatus.CONFIRMED);

    const notExistsResponse = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/verify/phones/${phoneNumberToNotExist}`)
      .expect(200);
    expect(notExistsResponse.body.data.verificationStatus).toBe(PhoneVerificationStatus.NOT_EXISTS);
  });
});
