#!/bin/bash
set -e
export NODE_ENV='test-local'

# shellcheck disable=SC2059
printf "\n Starting run-e2e-tests.sh with node env: ${NODE_ENV}\n"
# Function to stop and clean up the services
echo "Stopping and cleaning up local infrastructure for tests..."
docker-compose -f ./test/docker-compose-test.yml down -v
echo "Cleanup completed."

echo "Starting local infrastructure..."
docker-compose -f ./test/docker-compose-test.yml up -d

export DATABASE_URL="postgresql://postgres:password123@localhost:5444/transcendence_e2e_db"

# Wait for the database to be ready (adjust the time as needed)
echo "Waiting for the database to be ready..."
sleep 10

echo "Deploying Prisma migrations to the test database..."
npx prisma generate
npx prisma migrate deploy --schema=./prisma/schema

# Wait for OpenSearch to be ready
echo "Waiting for OpenSearch to be ready..."
until curl -s http://localhost:9210/_cluster/health | grep -q '"status":"green\|yellow"'; do
  echo "Waiting for OpenSearch..."
  sleep 5
done
echo "OpenSearch is ready!"

echo "Configuring localstack..."
export AWS_ACCESS_KEY_ID="test"
export AWS_SECRET_ACCESS_KEY="test"
export AWS_REGION="us-east-1"
export AWS_ENDPOINT_URL="http://localhost:4567"
export LOG_GROUP_NAME_TRANSCENDENCE="transcendence-staging"
export LOG_STREAM_NAME_TRANSCENDENCE="transcendence-staging"
export AWS_PAGER=""
export PORTFOLIO_IMPORT_FIDELEASY_QUEUE_URL="http://localhost:4567/000000000000/fideleasy_portfolio_import"
export PORTFOLIO_IMPORT_SALESZAP_QUEUE_URL="http://localhost:4567/000000000000/saleszap_portfolio_import"
export PORTFOLIO_IMPORT_COLLECTCASH_QUEUE_URL="http://localhost:4567/000000000000/collectcash_portfolio_import"
export PORTFOLIO_WORKFLOW_FIDELEASY_QUEUE_URL="http://localhost:4567/000000000000/fideleasy_portfolio_workflow"
export PORTFOLIO_WORKFLOW_SALESZAP_QUEUE_URL="http://localhost:4567/000000000000/saleszap_portfolio_workflow"
export PORTFOLIO_WORKFLOW_COLLECTCASH_QUEUE_URL="http://localhost:4567/000000000000/collectcash_portfolio_workflow"
export DEFAULT_PORTFOLIO_PROCESSING_RATE_LIMIT=100
export DEFAULT_PORTFOLIO_IDLE_AFTER=7
export OUTGOING_MESSAGE_QUEUE_URL="http://localhost:4567/000000000000/message_outgoing"
export INCOMING_MESSAGE_QUEUE_URL="http://localhost:4567/000000000000/message_incoming"
export DATA_INSIGHTS_VECTOR_STORE_HIRING_QUEUE_URL="http://localhost:4567/000000000000/data_insights_vector_store_hiring"
export MAX_DELAY_BETWEEN_MESSAGES=30
export BUSINESS_BASE_SERVICE_URL="http://localhost:3000"
export TRANSCENDENCE_ADMIN_WEB_URL="http://localhost:3390"
export MESSAGEHUB_SERVICE_URL="http://localhost:3000"
export INTELLIGENCE_SERVICE_URL="http://localhost:3000"
export JWT_SECRET="0#*%z!dk!S6K8%{jC%epR1!ed09mDht.eX9_4fY&Fi?YgvHW1xwetDF5L/tg4){tvPxkgvCmYu@e1Bh-9=1Qb&gYW4/Xt?00#}Se"
export SLACK_TOKEN= "slack_token_fake"
export VONAGE_API_KEY="ebe08b1c"
export VONAGE_API_SECRET="kqWCQ2kHErHLorEL"
export VONAGE_SIGNATURE_SECRET=""
export DIRECT_MESSAGE_FILES_BUCKET="transcendence-direct-message-files"
export PORTFOLIO_IMPORT_FILES_BUCKET="transcendence-portfolio-import-files"
export IMPORT_ITEM_QUEUE_PREFIX="portfolio-item-"
export IMPORT_ITEM_QUEUE_SUFIX="sufix"
export SQS_QUEUE_BASE_URL="http://localhost:4567/000000000000/"
export OPENSEARCH_NODE="http://localhost:9210"
export CHECK_NUMBER_SERVICE_URL="https://api.checknumber.ai"
export CHECK_NUMBER_API_KEY="FaPQ9gHO0epSi28lzVy1eEOQn7uF6ksxcbkpVzoIhjkkRdmLkkIlCfq5XCLb"

echo "Creating DynamoDB tables..."

aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/workflow-definition.json 
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/workflow-execution-definition.json 
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/message-history-definition.json 
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/portfolio-item-custom-data-definition.json
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/customer-channel-integration-data-definition.json
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/conversation-message-definition.json
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/middleware-response-output-definition.json
aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/customer-preferences-definition.json

echo "Creating S3 buckets..."
aws s3api create-bucket --bucket transcendence-direct-message-files --region us-east-1 --endpoint-url http://localhost:4567
aws s3api create-bucket --bucket transcendence-portfolio-import-files --region us-east-1 --endpoint-url http://localhost:4567

echo "Creating SQS queues..."
aws sqs create-queue --queue-name fideleasy_portfolio_import --endpoint-url http://localhost:4567
aws sqs create-queue --queue-name saleszap_portfolio_import --endpoint-url http://localhost:4567
aws sqs create-queue --queue-name collectcash_portfolio_import --endpoint-url http://localhost:4567

aws sqs create-queue --queue-name fideleasy_portfolio_workflow --endpoint-url http://localhost:4567
aws sqs create-queue --queue-name saleszap_portfolio_workflow --endpoint-url http://localhost:4567
aws sqs create-queue --queue-name collectcash_portfolio_workflow --endpoint-url http://localhost:4567
aws sqs create-queue --queue-name message_outgoing --endpoint-url http://localhost:4567
aws sqs create-queue --queue-name message_incoming --endpoint-url http://localhost:4567

aws sqs create-queue --queue-name data_insights_vector_store_hiring --endpoint-url http://localhost:4567

#echo "Seeding the database with initial data..."
npm run local-infra:seed

echo "Local infrastructure started!"

