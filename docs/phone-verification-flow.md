# Phone Verification Controller

This document provides an overview of the Phone Verification Controller, its API endpoints, and a detailed explanation of the phone number verification flow.

## API Endpoints

The `PhoneVerificationController` exposes several endpoints for managing phone number verification requests and statuses.

### 1. Create Phone Verification Requests

**Endpoint:** `POST /message-hub/verify/phones`

**Description:** This endpoint is used to initiate the verification process for one or more phone numbers associated with a customer. It accepts a list of phone numbers and a customer ID. If a phone number already has a pending verification request, it will not be re-created.

**Request Body (`PhoneVerificationDto`):**

| Field        | Type     | Description                                     | Required |
|--------------|----------|-------------------------------------------------|----------|
| `customerId` | `string` | The UUID of the customer.                       | Yes      |
| `phoneNumbers` | `string[]` | An array of phone numbers to be verified.       | Yes      |

**Example Request:**

```json
{
  "customerId": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
  "phoneNumbers": [
    "+15551234567",
    "+15557654321"
  ]
}
```

**Success Response (201 Created):**

```json
{
  "statusCode": 201,
  "data": [
    {
      "id": "some-uuid-1",
      "phoneNumber": "+15551234567"
    },
    {
      "id": "some-uuid-2",
      "phoneNumber": "+15557654321"
    }
  ]
}
```

**Error Response (400 Bad Request):**

This error occurs if validation fails or if all provided phone numbers already have pending verification requests.

```json
{
  "statusCode": 400,
  "message": "All phone numbers already have a verification request: +15551112222, +15553334444",
  "error": "Bad Request"
}
```

### 2. Retrieve Phone Verification Status

**Endpoint:** `GET /message-hub/verify/phones/:phoneNumber`

**Description:** This endpoint allows you to retrieve the current verification status of a specific phone number.

**Path Parameters:**

| Parameter     | Type     | Description                                     |
|---------------|----------|-------------------------------------------------|
| `phoneNumber` | `string` | The phone number for which to retrieve status.  |

**Example Request:**

```
GET /message-hub/verify/phones/+15559998888
```

**Success Response (200 OK):**

```json
{
  "statusCode": 200,
  "data": {
    "phoneNumber": "+15559998888",
    "verificationStatus": "PENDING" // or CONFIRMED, NOT_EXISTS
  }
}
```

**Error Response (404 Not Found):**

This error occurs if no verification record is found for the given phone number.

```json
{
  "statusCode": 404,
  "message": "Phone verification not found for phone number: +15550001111",
  "error": "Not Found"
}
```

### 3. Trigger Pending Verifications Check

**Endpoint:** `POST /message-hub/verify/phones/check`

**Description:** This endpoint triggers a manual check for all phone numbers that currently have a `PENDING` verification status. The system will attempt to re-verify these numbers and update their status accordingly (e.g., to `CONFIRMED` or `NOT_EXISTS`). This is typically used for scheduled background tasks but can be triggered manually for immediate re-evaluation.

**Example Request:**

```
POST /message-hub/verify/phones/check
```

**Success Response (200 OK):**

```json
{
  "statusCode": 200
}
```

## Phone Number Verification Flow

The phone number verification process involves several steps, from initial request to final status update. The flow is designed to handle asynchronous checks with external services.




### Verification Flowchart

```mermaid



graph TD
    A[Start Verification] --> B{Phone Numbers Provided?};
    B -- Yes --> C{Check for Existing Verifications};
    C --> D{Filter Out Existing Numbers};
    D -- No New Numbers --> E[Throw Business Exception];
    D -- New Numbers Exist --> F[Create New Phone Verification Records];
    F --> G[Trigger External Number Check Service];
    G --> H[Return Created Verification IDs];

    subgraph Check Pending Verifications Flow
        I[Start Check] --> J{Fetch All PENDING Verifications};
        J --> K{For Each Pending Verification};
        K --> L{Call External Number Check Service};
        L --> M{Receive Check Result};
        M -- whatsapp == 'yes' --> N[Set Status to CONFIRMED];
        M -- whatsapp == 'no' --> O[Set Status to NOT_EXISTS];
        M -- Other --> P[Keep Status as PENDING];
        N --> Q[Update Verification Record];
        O --> Q;
        P --> Q;
        Q --> R[Log Update];
        R --> K;
        K -- All Processed --> S[End Check];
    end
```

![Phone Verification Flowchart](https://private-us-east-1.manuscdn.com/sessionFile/GdkkBIHOio4PpY7zFSL7wW/sandbox/zysSaO62KF6XP4kZhZIiQm-images_1756411412816_na1fn_L2hvbWUvdWJ1bnR1L3ZlcmlmaWNhdGlvbl9mbG93.png?Policy=eyJTdGF0ZW1lbnQiOlt7IlJlc291cmNlIjoiaHR0cHM6Ly9wcml2YXRlLXVzLWVhc3QtMS5tYW51c2Nkbi5jb20vc2Vzc2lvbkZpbGUvR2Rra0JJSE9pbzRQcFk3ekZTTDd3Vy9zYW5kYm94L3p5c1NhTzYyS0Y2WFA0a1poWklpUW0taW1hZ2VzXzE3NTY0MTE0MTI4MTZfbmExZm5fTDJodmJXVXZkV0oxYm5SMUwzWmxjbWxtYVdOaGRHbHZibDltYkc5My5wbmciLCJDb25kaXRpb24iOnsiRGF0ZUxlc3NUaGFuIjp7IkFXUzpFcG9jaFRpbWUiOjE3OTg3NjE2MDB9fX1dfQ__&Key-Pair-Id=K2HSFNDJXOU9YS&Signature=T1uDBc4YkEC7ycYn~QrK3dvz27a4f7urV5KRHCq6exFL2MRQs3XENp8nrn45tEoJYdP9cjk0NNQPp74wN6dijl1hu3dOjQZMKTVSx~Ne~gXSgiHHRRwiXznuSsp7hQ6rTqPzo6p-NHJdKw33a0SUKdTI-aH3dZF~mUEoEly7bakjyfVHIapFY8nEHJ1ySF8xQ0xGWDo6Z7kURcPuyl6F1FobVp2bhQVR-SMfnjWC3jHxatmxWvAI34y8O4ab-lt6mNb9DWjqjIipQpiex7063favUtX7G57OLIo1jx4noqzhaECYzOJrQZMHeZZvsq3Q7Bhg-BO49tHKpPqAT2SBLg__)


